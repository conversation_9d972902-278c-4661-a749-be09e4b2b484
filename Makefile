# Project Configuration
Server = .
ServerName = xbit-hypertrader
GOOS = linux
GOARCH = amd64
BinDir = ./bin

# Detect local platform
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

# Build targets
build: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go

build-local: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go

build-scheduler: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName)-scheduler cmd/scheduler/main.go

build-worker: gqlgen-check
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName)-worker cmd/worker_kline/main.go

build-all: build-local build-scheduler build-worker

# GraphQL generation
gqlgen:
	@echo "Generating GraphQL code..."
	go run github.com/99designs/gqlgen generate --config gqlgen.yml

gqlgen-check:
	@if [ ! -f "internal/controller/graphql/generated.go" ]; then \
		echo "Generated GraphQL files not found, generating..."; \
		$(MAKE) gqlgen; \
	fi

# Database operations
db-create-tables:
	@echo "Creating all database tables..."
	./scripts/run.sh local create-tables

db-create-clickhouse-tables:
	@echo "Creating ClickHouse tables only..."
	./scripts/run.sh local create-clickhouse-tables

# Testing
test:
	go test ./...

test-verbose:
	go test -v ./...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Development
run-local:
	./scripts/run.sh local run

run-scheduler:
	./scripts/run.sh local scheduler

run-worker:
	./scripts/run.sh local worker

run-kline:
	./scripts/run.sh local kline

dev:
	./scripts/run.sh local dev

# Dependencies
deps:
	go mod tidy
	go mod download

# Linting and formatting
fmt:
	go fmt ./...

vet:
	go vet ./...

lint:
	golangci-lint run

# Docker operations
docker-build:
	docker build -t $(ServerName):latest .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

# Cleanup
clean:
	rm -rf $(BinDir)
	rm -f coverage.out coverage.html

clean-all: clean
	go clean -cache
	go clean -modcache

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build for Linux (production)"
	@echo "  build-local    - Build for local platform"
	@echo "  build-all      - Build all services"
	@echo "  gqlgen         - Generate GraphQL code"
	@echo "  db-create-tables - Create ClickHouse tables"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  run-local      - Run GraphQL server locally"
	@echo "  run-scheduler  - Run scheduler locally"
	@echo "  run-worker     - Run worker locally"
	@echo "  run-kline      - Run kline worker locally"
	@echo "  dev            - Run with hot reload"
	@echo "  deps           - Download dependencies"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"
	@echo "  lint           - Run linter"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run with Docker Compose"
	@echo "  clean          - Clean build artifacts"
	@echo "  help           - Show this help"

.PHONY: build build-local build-scheduler build-worker build-all gqlgen gqlgen-check db-create-tables test test-verbose test-coverage run-local run-scheduler run-worker dev deps fmt vet lint docker-build docker-run docker-stop docker-logs clean clean-all help
