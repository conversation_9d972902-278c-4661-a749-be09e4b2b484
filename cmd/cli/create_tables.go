package main

import (
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Creating database tables...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Create TiDB tables using GORM auto-migration
	if global.GVA_DB_TIDB != nil {
		global.GVA_LOG.Info("Creating TiDB tables...")

		err := global.GVA_DB_TIDB.AutoMigrate(
			&model.User{},
			&model.NodeFill{},
		)
		if err != nil {
			global.GVA_LOG.Error("Failed to create TiDB tables, continuing with ClickHouse...", zap.Error(err))
		} else {
			global.GVA_LOG.Info("TiDB tables created successfully")
		}
	} else {
		global.GVA_LOG.Warn("TiDB connection is nil, skipping TiDB table creation")
	}

	// Create ClickHouse tables using raw SQL
	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_LOG.Info("Creating ClickHouse tables...")

		// Create analytics tables
		analyticsTables := []string{
			model.CreateTradeAnalyticsTable,
			model.CreateUserAnalyticsTable,
			model.CreateMarketDataTable,
		}

		for _, tableSQL := range analyticsTables {
			_, err := global.GVA_DB_CLICKHOUSE.Exec(tableSQL)
			if err != nil {
				global.GVA_LOG.Fatal("Failed to create ClickHouse analytics table", zap.Error(err))
			}
		}

		// Create kline tables
		global.GVA_LOG.Info("Creating ClickHouse kline tables...")
		klineTables := model.GetAllKlineTableCreationSQL()

		for _, tableSQL := range klineTables {
			_, err := global.GVA_DB_CLICKHOUSE.Exec(tableSQL)
			if err != nil {
				global.GVA_LOG.Fatal("Failed to create ClickHouse kline table", zap.Error(err))
			}
		}

		global.GVA_LOG.Info("ClickHouse tables created successfully")
	}

	global.GVA_LOG.Info("All database tables created successfully")
}
