package main

import (
"go.uber.org/zap"

"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

func main() {
// Initialize configuration
global.GVA_VP = initializer.Viper()
if global.GVA_VP == nil {
panic("Failed to initialize Viper configuration")
}

// Initialize logger
initializer.InitZap()
if global.GVA_LOG == nil {
panic("Failed to initialize logger")
}
global.GVA_LOG.Info("Creating ClickHouse tables only...")

// Initialize only ClickHouse database
global.GVA_DB_CLICKHOUSE = initializer.InitClickHouse()
defer func() {
if global.GVA_DB_CLICKHOUSE != nil {
if err := global.GVA_DB_CLICKHOUSE.Close(); err != nil {
global.GVA_LOG.Error("Failed to close ClickHouse connection: " + err.Error())
} else {
global.GVA_LOG.Info("ClickHouse connection closed successfully")
}
}
}()

// Create ClickHouse tables using raw SQL
if global.GVA_DB_CLICKHOUSE != nil {
global.GVA_LOG.Info("Creating ClickHouse analytics tables...")

// Create analytics tables
analyticsTables := []string{
model.CreateTradeAnalyticsTable,
model.CreateUserAnalyticsTable,
model.CreateMarketDataTable,
}

for i, tableSQL := range analyticsTables {
global.GVA_LOG.Info("Creating analytics table", zap.Int("index", i+1))
_, err := global.GVA_DB_CLICKHOUSE.Exec(tableSQL)
if err != nil {
global.GVA_LOG.Fatal("Failed to create ClickHouse analytics table", 
zap.Int("table_index", i+1), 
zap.Error(err))
}
global.GVA_LOG.Info("Analytics table created successfully", zap.Int("index", i+1))
}

// Create kline tables
global.GVA_LOG.Info("Creating ClickHouse kline tables...")
klineTables := model.GetAllKlineTableCreationSQL()

for i, tableSQL := range klineTables {
global.GVA_LOG.Info("Creating kline table", zap.Int("index", i+1))
_, err := global.GVA_DB_CLICKHOUSE.Exec(tableSQL)
if err != nil {
global.GVA_LOG.Fatal("Failed to create ClickHouse kline table", 
zap.Int("table_index", i+1), 
zap.Error(err))
}
global.GVA_LOG.Info("Kline table created successfully", zap.Int("index", i+1))
}

global.GVA_LOG.Info("All ClickHouse tables created successfully")
} else {
global.GVA_LOG.Fatal("ClickHouse connection is nil")
}

global.GVA_LOG.Info("ClickHouse table creation completed")
}
