package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/service"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()

	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader kline worker...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Initialize kline service
	klineService := service.NewKlineService()

	// Start worker
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go runKlineWorker(ctx, klineService)

	// Wait for shutdown signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down kline worker...")
	cancel()
	global.GVA_LOG.Info("Kline worker exited")
}

func runKlineWorker(ctx context.Context, klineService *service.KlineService) {
	global.GVA_LOG.Info("Starting kline worker in batch processing mode (NATS disabled)")

	// Start periodic batch processing
	go runPeriodicBatchProcessor(ctx, klineService)

	global.GVA_LOG.Info("Kline worker started, running batch processing every minute...")
	<-ctx.Done()
}

func runPeriodicBatchProcessor(ctx context.Context, klineService *service.KlineService) {
	ticker := time.NewTicker(1 * time.Minute) // Process every minute
	defer ticker.Stop()

	var lastProcessedTime time.Time

	// Initialize with current time minus 5 minutes to catch recent fills
	lastProcessedTime = time.Now().Add(-15000 * time.Minute)

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// Process fills since last run
			fills, err := klineService.GetFillsSince(ctx, lastProcessedTime, 1000)
			if err != nil {
				global.GVA_LOG.Error("Failed to get fills for batch processing", zap.Error(err))
				continue
			}

			if len(fills) > 0 {
				global.GVA_LOG.Info("Processing batch of fills for klines",
					zap.Int("count", len(fills)))

				if err := klineService.ProcessFillsToKlines(ctx, fills); err != nil {
					global.GVA_LOG.Error("Failed to process batch fills to klines", zap.Error(err))
					continue
				}

				// Update last processed time to the latest fill time
				if len(fills) > 0 {
					latestFill := fills[len(fills)-1]
					lastProcessedTime = latestFill.GetExecutedTime()
				}

				global.GVA_LOG.Info("Batch processing completed",
					zap.Int("processed", len(fills)),
					zap.Time("last_processed", lastProcessedTime))
			}
		}
	}
}
