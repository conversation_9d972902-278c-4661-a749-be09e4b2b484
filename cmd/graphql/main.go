package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()
	
	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader GraphQL server...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Set Gin mode based on environment
	switch global.GVA_CONFIG.System.Env {
	case "local", "dev", "development":
		gin.SetMode(gin.DebugMode)
	case "test":
		gin.SetMode(gin.TestMode)
	default:
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Configure CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	router.Use(cors.New(config))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "xbit-hypertrader",
			"timestamp": time.Now().UTC(),
			"databases": gin.H{
				"tidb":       global.GVA_DB_TIDB != nil,
				"clickhouse": global.GVA_DB_CLICKHOUSE != nil,
			},
		})
	})

	// API routes group
	api := router.Group(global.GVA_CONFIG.System.RouterPrefix)
	{
		api.GET("/ping", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
				"time":    time.Now().UTC(),
			})
		})

		// TODO: Add GraphQL endpoints here
		// graphql := api.Group("/graphql")
		// {
		//     graphql.POST("/", graphqlHandler())
		//     graphql.GET("/", playgroundHandler())
		// }
	}

	// Create HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		global.GVA_LOG.Info(fmt.Sprintf("Server starting on port %d", global.GVA_CONFIG.System.Addr))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			global.GVA_LOG.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		global.GVA_LOG.Error("Server forced to shutdown", zap.Error(err))
	}

	global.GVA_LOG.Info("Server exited")
}
