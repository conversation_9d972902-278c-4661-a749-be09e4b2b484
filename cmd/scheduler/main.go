package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/initializer"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/task"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()
	
	// Initialize logger
	initializer.InitZap()
	global.GVA_LOG.Info("Starting xbit-hypertrader scheduler...")

	// Initialize databases
	initializer.InitDatabases()
	defer initializer.CloseDatabases()

	// Create task scheduler
	scheduler := task.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Register scheduled tasks
	registerTasks(scheduler)

	// Start scheduler in a goroutine
	go scheduler.RunWithSignal(ctx)

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	global.GVA_LOG.Info("Shutting down scheduler...")
	cancel()
	global.GVA_LOG.Info("Scheduler exited")
}

func registerTasks(scheduler *task.TaskScheduler) {
	// Register your scheduled tasks here
	for _, taskConfig := range global.GVA_CONFIG.CronTasks {
		switch taskConfig.ID {
		case "market_data_sync":
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Info("Executing market data sync task")
				// TODO: Implement market data synchronization logic
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register market_data_sync task", zap.Error(err))
			}

		case "analytics_aggregation":
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Info("Executing analytics aggregation task")
				// TODO: Implement analytics aggregation logic
				// This would typically read from TiDB and write aggregated data to ClickHouse
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register analytics_aggregation task", zap.Error(err))
			}

		case "cleanup_old_data":
			err := scheduler.Register(taskConfig.ID, taskConfig.Cron, func() {
				global.GVA_LOG.Info("Executing cleanup old data task")
				// TODO: Implement data cleanup logic
			})
			if err != nil {
				global.GVA_LOG.Error("Failed to register cleanup_old_data task", zap.Error(err))
			}

		default:
			global.GVA_LOG.Warn("Unknown task ID", zap.String("task_id", taskConfig.ID))
		}
	}
}
