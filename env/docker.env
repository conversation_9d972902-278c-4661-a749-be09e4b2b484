# Application Configuration
APP_ENV=docker
APP_NAME=xbit-hypertrader
SERVER_PORT=8080

# TiDB Configuration (OLTP - Transactional workloads)
TIDB_HOST=**********
TIDB_PORT=4000
TIDB_USER=root
TIDB_PASS=XN9YY2P2gl41IoqjQdnQEomHIivuwLCZ
TIDB_DB=test
TIDB_CONFIG=charset=utf8mb4&parseTime=True&loc=Local
TIDB_MAX_IDLE_CONNS=10
TIDB_MAX_OPEN_CONNS=100
TIDB_MAX_LIFETIME=1h

# ClickHouse Configuration (OLAP - Analytics workloads)
CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=8123
CLICKHOUSE_USER=dex
CLICKHOUSE_PASS=Zww9fKzH4MbI19K3PrT0WIXFnvmFSvKAEUV4r0aqAho0
CLICKHOUSE_DB=default

# JWT Configuration
JWT_SECRET=docker-secret-key-xbit-hypertrader
JWT_EXPIRES_TIME=7d
JWT_ISSUER=xbit-hypertrader-docker

# NATS Configuration
NATS_URL=nats://nats:4222
