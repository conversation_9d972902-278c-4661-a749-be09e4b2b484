package config

type Server struct {
	JWT       JWT       `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	System    System    `mapstructure:"system" json:"system" yaml:"system"`
	TiDB      TiDB      `mapstructure:"tidb" json:"tidb" yaml:"tidb"`
	ClickHouse ClickHouse `mapstructure:"clickhouse" json:"clickhouse" yaml:"clickhouse"`
	Nats      Nats      `mapstructure:"nats" json:"nats" yaml:"nats"`
	CronTasks []Task    `mapstructure:"cron-tasks" json:"cron-tasks" yaml:"cron-tasks"`
}

type JWT struct {
	SigningKey  string `mapstructure:"signing-key" json:"signing-key" yaml:"signing-key"`
	ExpiresTime string `mapstructure:"expires-time" json:"expires-time" yaml:"expires-time"`
	BufferTime  string `mapstructure:"buffer-time" json:"buffer-time" yaml:"buffer-time"`
	Issuer      string `mapstructure:"issuer" json:"issuer" yaml:"issuer"`
}

type System struct {
	Env                 string `mapstructure:"env" json:"env" yaml:"env"`
	Addr                int    `mapstructure:"addr" json:"addr" yaml:"addr"`
	RouterPrefix        string `mapstructure:"router-prefix" json:"router-prefix" yaml:"router-prefix"`
	GraphqlPrefix       string `mapstructure:"graphql-prefix" json:"graphql-prefix" yaml:"graphql-prefix"`
	AdminGraphqlPrefix  string `mapstructure:"admin-graphql-prefix" json:"admin-graphql-prefix" yaml:"admin-graphql-prefix"`
}

// TiDB configuration for transactional/OLTP workloads
type TiDB struct {
	Host         string `mapstructure:"host" json:"host" yaml:"host"`
	Port         string `mapstructure:"port" json:"port" yaml:"port"`
	Config       string `mapstructure:"config" json:"config" yaml:"config"`
	Dbname       string `mapstructure:"db-name" json:"db-name" yaml:"db-name"`
	Username     string `mapstructure:"username" json:"username" yaml:"username"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"max-idle-conns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"max-open-conns" yaml:"max-open-conns"`
	MaxLifetime  string `mapstructure:"max-lifetime" json:"max-lifetime" yaml:"max-lifetime"`
}

// ClickHouse configuration for analytics/OLAP workloads
type ClickHouse struct {
	Host         string `mapstructure:"host" json:"host" yaml:"host"`
	Port         string `mapstructure:"port" json:"port" yaml:"port"`
	Database     string `mapstructure:"database" json:"database" yaml:"database"`
	Username     string `mapstructure:"username" json:"username" yaml:"username"`
	Password     string `mapstructure:"password" json:"password" yaml:"password"`
	MaxIdleConns int    `mapstructure:"max-idle-conns" json:"max-idle-conns" yaml:"max-idle-conns"`
	MaxOpenConns int    `mapstructure:"max-open-conns" json:"max-open-conns" yaml:"max-open-conns"`
	MaxLifetime  string `mapstructure:"max-lifetime" json:"max-lifetime" yaml:"max-lifetime"`
	Secure       bool   `mapstructure:"secure" json:"secure" yaml:"secure"`
	SkipVerify   bool   `mapstructure:"skip-verify" json:"skip-verify" yaml:"skip-verify"`
}

type Nats struct {
	URL    string `mapstructure:"url" json:"url" yaml:"url"`
	User   string `mapstructure:"user" json:"user" yaml:"user"`
	Pass   string `mapstructure:"pass" json:"pass" yaml:"pass"`
	UseTLS bool   `mapstructure:"use-tls" json:"use-tls" yaml:"use-tls"`
	Token  string `mapstructure:"token" json:"token" yaml:"token"`
}

type Task struct {
	ID   string `mapstructure:"id" json:"id" yaml:"id"`
	Cron string `mapstructure:"cron" json:"cron" yaml:"cron"`
}
