#!/bin/sh
set -e

_terminate() {
  echo "Signal received. Waiting for graceful shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Create database tables if needed
if [ "$1" = "./xbit-hypertrader" ]; then
  echo "Creating database tables..."
  go run cmd/cli/create_tables.go || echo "Warning: Failed to create tables, continuing..."
fi

# Execute the main command
exec $@
