package initializer

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

func Zap() *zap.Logger {
	var level zapcore.Level
	switch global.GVA_CONFIG.System.Env {
	case "local", "dev", "development":
		level = zap.DebugLevel
	case "test":
		level = zap.ErrorLevel
	default:
		level = zap.InfoLevel
	}

	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: global.GVA_CONFIG.System.Env == "local",
		Encoding:    "json",
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "timestamp",
			LevelKey:       "level",
			NameKey:        "logger",
			Caller<PERSON>ey:      "caller",
			FunctionKey:    zapcore.OmitKey,
			MessageKey:     "message",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	// Use console encoder for local development
	if global.GVA_CONFIG.System.Env == "local" {
		config.Encoding = "console"
		config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return logger
}

// InitZap initializes the global logger
func InitZap() {
	global.GVA_LOG = Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
}
