package initializer

import (
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

// InitTiDB initializes TiDB connection for OLTP workloads
func InitTiDB() *gorm.DB {
	tidb := global.GVA_CONFIG.TiDB
	if tidb.Dbname == "" {
		global.GVA_LOG.Info("TiDB database name is empty, skipping TiDB initialization")
		return nil
	}

	// Build DSN for TiDB (MySQL-compatible)
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?%s",
		tidb.Username,
		tidb.Password,
		tidb.Host,
		tidb.Port,
		tidb.Dbname,
		tidb.Config,
	)

	// Configure GORM logger based on environment
	var gormLogger logger.Interface
	switch global.GVA_CONFIG.System.Env {
	case "local", "dev", "development":
		gormLogger = logger.Default.LogMode(logger.Info)
	case "test":
		gormLogger = logger.Default.LogMode(logger.Silent)
	default:
		gormLogger = logger.Default.LogMode(logger.Warn)
	}

	// Open TiDB connection
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                 gormLogger,
		DisableForeignKeyConstraintWhenMigrating: true, // TiDB doesn't support foreign keys
	})
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to connect to TiDB: %v", err))
		panic("Failed to connect to TiDB: " + err.Error())
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to get TiDB sql.DB: %v", err))
		panic("Failed to get TiDB sql.DB: " + err.Error())
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(tidb.MaxIdleConns)
	sqlDB.SetMaxOpenConns(tidb.MaxOpenConns)
	
	// Parse max lifetime
	if tidb.MaxLifetime != "" {
		if lifetime, err := time.ParseDuration(tidb.MaxLifetime); err == nil {
			sqlDB.SetConnMaxLifetime(lifetime)
		} else {
			global.GVA_LOG.Warn(fmt.Sprintf("Invalid TiDB max lifetime format: %s, using default", tidb.MaxLifetime))
			sqlDB.SetConnMaxLifetime(time.Hour)
		}
	} else {
		sqlDB.SetConnMaxLifetime(time.Hour)
	}

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("Failed to ping TiDB: %v", err))
		panic("Failed to ping TiDB: " + err.Error())
	}

	global.GVA_LOG.Info("TiDB connection established successfully")
	return db
}

// GetTiDB returns the global TiDB connection
func GetTiDB() *gorm.DB {
	if global.GVA_DB_TIDB == nil {
		global.GVA_LOG.Error("TiDB connection is not initialized")
		return nil
	}
	return global.GVA_DB_TIDB
}
