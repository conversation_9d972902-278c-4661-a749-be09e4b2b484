package global

import (
	"database/sql"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
)

var (
	GVA_DB_TIDB       *gorm.DB      // TiDB connection for OLTP workloads
	GVA_DB_CLICKHOUSE *sql.DB       // ClickHouse connection for OLAP workloads
	GVA_CONFIG        config.Server // Global configuration
	GVA_VP            *viper.Viper  // Viper instance
	GVA_LOG           *zap.Logger   // Logger instance
)
