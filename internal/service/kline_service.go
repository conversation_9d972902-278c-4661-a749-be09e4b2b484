package service

import (
	"context"
	"database/sql"
	"fmt"
	"sort"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

type KlineService struct {
	tidb       *gorm.DB
	clickhouse *sql.DB
}

func NewKlineService() *KlineService {
	return &KlineService{
		tidb:       global.GVA_DB_TIDB,
		clickhouse: global.GVA_DB_CLICKHOUSE,
	}
}

// ProcessFillsToKlines processes node fills and generates kline data for all timeframes
func (s *KlineService) ProcessFillsToKlines(ctx context.Context, fills []model.NodeFill) error {
	if len(fills) == 0 {
		return nil
	}

	// Group fills by symbol
	fillsBySymbol := make(map[string][]model.NodeFill)
	for _, fill := range fills {
		if !fill.IsValidForKline() {
			global.GVA_LOG.Warn("Invalid fill for kline generation", 
				zap.Int64("fill_id", fill.ID),
				zap.String("symbol", fill.Coin))
			continue
		}
		fillsBySymbol[fill.Coin] = append(fillsBySymbol[fill.Coin], fill)
	}

	// Process each symbol
	for symbol, symbolFills := range fillsBySymbol {
		if err := s.processSymbolFills(ctx, symbol, symbolFills); err != nil {
			global.GVA_LOG.Error("Failed to process fills for symbol",
				zap.String("symbol", symbol),
				zap.Error(err))
			return err
		}
	}

	return nil
}

// processSymbolFills processes fills for a specific symbol across all timeframes
func (s *KlineService) processSymbolFills(ctx context.Context, symbol string, fills []model.NodeFill) error {
	// Sort fills by timestamp
	sort.Slice(fills, func(i, j int) bool {
		return fills[i].Time < fills[j].Time
	})

	// Process each timeframe
	for _, timeframe := range model.GetAllTimeframes() {
		if err := s.generateKlinesForTimeframe(ctx, symbol, fills, timeframe); err != nil {
			global.GVA_LOG.Error("Failed to generate klines for timeframe",
				zap.String("symbol", symbol),
				zap.String("timeframe", string(timeframe)),
				zap.Error(err))
			return err
		}
	}

	return nil
}

// generateKlinesForTimeframe generates kline data for a specific timeframe
func (s *KlineService) generateKlinesForTimeframe(ctx context.Context, symbol string, fills []model.NodeFill, timeframe model.KlineTimeframe) error {
	// Group fills by time buckets
	klineMap := make(map[time.Time]*model.Kline)

	for _, fill := range fills {
		fillTime := fill.GetExecutedTime()
		bucketTime := timeframe.TruncateTime(fillTime)

		kline, exists := klineMap[bucketTime]
		if !exists {
			// Create new kline
			price, _ := fill.Px.Float64()
			volume, _ := fill.Sz.Float64()
			
			kline = &model.Kline{
				Timestamp:   bucketTime,
				Symbol:      symbol,
				Open:        price,
				High:        price,
				Low:         price,
				Close:       price,
				Volume:      volume,
				TradesCount: 1,
			}
			klineMap[bucketTime] = kline
		} else {
			// Update existing kline
			price, _ := fill.Px.Float64()
			volume, _ := fill.Sz.Float64()

			// Update OHLC
			if price > kline.High {
				kline.High = price
			}
			if price < kline.Low {
				kline.Low = price
			}
			kline.Close = price // Last price becomes close

			// Update volume and trade count
			kline.Volume += volume
			kline.TradesCount++
		}
	}

	// Save klines to ClickHouse
	for _, kline := range klineMap {
		if err := s.upsertKline(ctx, kline, timeframe); err != nil {
			return err
		}
	}

	return nil
}

// upsertKline inserts or updates a kline record in ClickHouse
func (s *KlineService) upsertKline(ctx context.Context, kline *model.Kline, timeframe model.KlineTimeframe) error {
	tableName := timeframe.GetTableName()

	// First, check if the record exists
	existingKline, err := s.getExistingKline(ctx, tableName, kline.Symbol, kline.Timestamp)
	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("failed to check existing kline: %w", err)
	}

	if existingKline != nil {
		// Update existing record by merging data
		mergedKline := s.mergeKlines(existingKline, kline)
		return s.updateKline(ctx, tableName, mergedKline)
	} else {
		// Insert new record
		return s.insertKline(ctx, tableName, kline)
	}
}

// getExistingKline retrieves an existing kline record
func (s *KlineService) getExistingKline(ctx context.Context, tableName, symbol string, timestamp time.Time) (*model.Kline, error) {
	query := fmt.Sprintf(`
		SELECT timestamp, symbol, open, high, low, close, volume, trades_count
		FROM %s
		WHERE symbol = ? AND timestamp = ?
		LIMIT 1
	`, tableName)

	row := s.clickhouse.QueryRowContext(ctx, query, symbol, timestamp)

	var kline model.Kline
	err := row.Scan(
		&kline.Timestamp,
		&kline.Symbol,
		&kline.Open,
		&kline.High,
		&kline.Low,
		&kline.Close,
		&kline.Volume,
		&kline.TradesCount,
	)

	if err != nil {
		return nil, err
	}

	return &kline, nil
}

// mergeKlines merges two kline records (existing + new data)
func (s *KlineService) mergeKlines(existing, new *model.Kline) *model.Kline {
	merged := &model.Kline{
		Timestamp: existing.Timestamp,
		Symbol:    existing.Symbol,
		Open:      existing.Open, // Keep original open
		High:      existing.High,
		Low:       existing.Low,
		Close:     new.Close, // Use new close
		Volume:    existing.Volume + new.Volume,
		TradesCount: existing.TradesCount + new.TradesCount,
	}

	// Update high and low
	if new.High > merged.High {
		merged.High = new.High
	}
	if new.Low < merged.Low {
		merged.Low = new.Low
	}

	return merged
}

// insertKline inserts a new kline record
func (s *KlineService) insertKline(ctx context.Context, tableName string, kline *model.Kline) error {
	query := fmt.Sprintf(`
		INSERT INTO %s (timestamp, symbol, open, high, low, close, volume, trades_count)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, tableName)

	_, err := s.clickhouse.ExecContext(ctx, query,
		kline.Timestamp,
		kline.Symbol,
		kline.Open,
		kline.High,
		kline.Low,
		kline.Close,
		kline.Volume,
		kline.TradesCount,
	)

	if err != nil {
		return fmt.Errorf("failed to insert kline: %w", err)
	}

	global.GVA_LOG.Debug("Inserted kline",
		zap.String("table", tableName),
		zap.String("symbol", kline.Symbol),
		zap.Time("timestamp", kline.Timestamp))

	return nil
}

// updateKline updates an existing kline record using ALTER TABLE UPDATE
func (s *KlineService) updateKline(ctx context.Context, tableName string, kline *model.Kline) error {
	query := fmt.Sprintf(`
		ALTER TABLE %s UPDATE
			open = ?,
			high = ?,
			low = ?,
			close = ?,
			volume = ?,
			trades_count = ?
		WHERE symbol = ? AND timestamp = ?
	`, tableName)

	_, err := s.clickhouse.ExecContext(ctx, query,
		kline.Open,
		kline.High,
		kline.Low,
		kline.Close,
		kline.Volume,
		kline.TradesCount,
		kline.Symbol,
		kline.Timestamp,
	)

	if err != nil {
		return fmt.Errorf("failed to update kline: %w", err)
	}

	global.GVA_LOG.Debug("Updated kline",
		zap.String("table", tableName),
		zap.String("symbol", kline.Symbol),
		zap.Time("timestamp", kline.Timestamp))

	return nil
}

// GetFillsSince retrieves fills from TiDB since a specific timestamp
func (s *KlineService) GetFillsSince(ctx context.Context, since time.Time, limit int) ([]model.NodeFill, error) {
	var fills []model.NodeFill

	sinceMs := since.UnixMilli()
	
	query := s.tidb.WithContext(ctx).
		Where("time > ?", sinceMs).
		Order("time ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&fills).Error; err != nil {
		return nil, fmt.Errorf("failed to get fills since %v: %w", since, err)
	}

	return fills, nil
}

// GetFillsInRange retrieves fills within a specific time range
func (s *KlineService) GetFillsInRange(ctx context.Context, start, end time.Time, symbol string) ([]model.NodeFill, error) {
	var fills []model.NodeFill

	startMs := start.UnixMilli()
	endMs := end.UnixMilli()

	query := s.tidb.WithContext(ctx).
		Where("time >= ? AND time <= ?", startMs, endMs).
		Order("time ASC")

	if symbol != "" {
		query = query.Where("coin = ?", symbol)
	}

	if err := query.Find(&fills).Error; err != nil {
		return nil, fmt.Errorf("failed to get fills in range %v to %v: %w", start, end, err)
	}

	return fills, nil
}
