package nats

import (
	"crypto/tls"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

type NATSClient struct {
	conn *nats.Conn
	js   nats.JetStreamContext
}

func InitNatsJetStream(natConfig config.Nats) *NATSClient {
	var tlsConfig *tls.Config
	if natConfig.UseTLS {
		tlsConfig = &tls.Config{
			MinVersion: tls.VersionTLS12,
		}
	}

	natsOptions := []nats.Option{
		nats.Timeout(10 * time.Second),
		nats.MaxReconnects(-1),
		nats.ReconnectWait(2 * time.Second),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			global.GVA_LOG.Error("NATS disconnected", zap.Error(err))
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			global.GVA_LOG.Info("NATS reconnected", zap.String("url", nc.ConnectedUrl()))
		}),
	}

	if tlsConfig != nil {
		natsOptions = append(natsOptions, nats.Secure(tlsConfig))
	}

	if natConfig.Token != "" {
		natsOptions = append(natsOptions, nats.Token(natConfig.Token))
	} else if natConfig.User != "" || natConfig.Pass != "" {
		natsOptions = append(natsOptions, nats.UserInfo(natConfig.User, natConfig.Pass))
	}

	nc, err := nats.Connect(natConfig.URL, natsOptions...)
	if err != nil {
		global.GVA_LOG.Fatal("Cannot connect to NATS", zap.Error(err))
	}

	js, err := nc.JetStream()
	if err != nil {
		global.GVA_LOG.Fatal("Cannot create JetStream", zap.Error(err))
	}

	global.GVA_LOG.Info("Connected to NATS JetStream successfully!")
	return &NATSClient{conn: nc, js: js}
}

func (n *NATSClient) Publish(subject string, data []byte) error {
	return n.conn.Publish(subject, data)
}

func (n *NATSClient) PublishJS(subject string, data []byte, opts ...nats.PubOpt) (*nats.PubAck, error) {
	return n.js.Publish(subject, data, opts...)
}

func (n *NATSClient) Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error) {
	return n.conn.Subscribe(subject, handler)
}

func (n *NATSClient) SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error) {
	return n.js.Subscribe(subject, handler, opts...)
}

func (n *NATSClient) Close() {
	if n.conn != nil && n.conn.Status() == nats.CONNECTED {
		global.GVA_LOG.Info("Closing NATS connection...")
		n.conn.Close()
	}
}
