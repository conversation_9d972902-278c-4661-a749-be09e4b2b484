package model

import (
	"time"
)

// <PERSON><PERSON> represents OHLCV candlestick data for ClickHouse storage
type Kline struct {
	Timestamp   time.Time `json:"timestamp"`
	Symbol      string    `json:"symbol"`
	Open        float64   `json:"open"`
	High        float64   `json:"high"`
	Low         float64   `json:"low"`
	Close       float64   `json:"close"`
	Volume      float64   `json:"volume"`
	TradesCount int64     `json:"trades_count"`
}

// KlineTimeframe represents different timeframe intervals
type KlineTimeframe string

const (
	Timeframe1m  KlineTimeframe = "1m"
	Timeframe3m  KlineTimeframe = "3m"
	Timeframe5m  KlineTimeframe = "5m"
	Timeframe15m KlineTimeframe = "15m"
	Timeframe30m KlineTimeframe = "30m"
	Timeframe1h  KlineTimeframe = "1h"
	Timeframe2h  KlineTimeframe = "2h"
	Timeframe4h  KlineTimeframe = "4h"
	Timeframe8h  KlineTimeframe = "8h"
	Timeframe12h KlineTimeframe = "12h"
	Timeframe1d  KlineTimeframe = "1d"
	Timeframe3d  KlineTimeframe = "3d"
	Timeframe1w  KlineTimeframe = "1w"
	Timeframe1mo KlineTimeframe = "1mo"
)

// GetAllTimeframes returns all supported timeframes
func GetAllTimeframes() []KlineTimeframe {
	return []KlineTimeframe{
		Timeframe1m, Timeframe3m, Timeframe5m, Timeframe15m, Timeframe30m,
		Timeframe1h, Timeframe2h, Timeframe4h, Timeframe8h, Timeframe12h,
		Timeframe1d, Timeframe3d, Timeframe1w, Timeframe1mo,
	}
}

// GetTimeframeMinutes returns the number of minutes for each timeframe
func (tf KlineTimeframe) GetMinutes() int {
	switch tf {
	case Timeframe1m:
		return 1
	case Timeframe3m:
		return 3
	case Timeframe5m:
		return 5
	case Timeframe15m:
		return 15
	case Timeframe30m:
		return 30
	case Timeframe1h:
		return 60
	case Timeframe2h:
		return 120
	case Timeframe4h:
		return 240
	case Timeframe8h:
		return 480
	case Timeframe12h:
		return 720
	case Timeframe1d:
		return 1440
	case Timeframe3d:
		return 4320
	case Timeframe1w:
		return 10080
	case Timeframe1mo:
		return 43200 // Approximate 30 days
	default:
		return 1
	}
}

// GetTableName returns the ClickHouse table name for the timeframe
func (tf KlineTimeframe) GetTableName() string {
	return "kline_" + string(tf)
}

// TruncateTime truncates the given time to the timeframe boundary
func (tf KlineTimeframe) TruncateTime(t time.Time) time.Time {
	switch tf {
	case Timeframe1m:
		return t.Truncate(time.Minute)
	case Timeframe3m:
		return t.Truncate(3 * time.Minute)
	case Timeframe5m:
		return t.Truncate(5 * time.Minute)
	case Timeframe15m:
		return t.Truncate(15 * time.Minute)
	case Timeframe30m:
		return t.Truncate(30 * time.Minute)
	case Timeframe1h:
		return t.Truncate(time.Hour)
	case Timeframe2h:
		return t.Truncate(2 * time.Hour)
	case Timeframe4h:
		return t.Truncate(4 * time.Hour)
	case Timeframe8h:
		return t.Truncate(8 * time.Hour)
	case Timeframe12h:
		return t.Truncate(12 * time.Hour)
	case Timeframe1d:
		return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	case Timeframe3d:
		// Truncate to 3-day periods starting from Unix epoch
		days := t.Unix() / (3 * 24 * 3600)
		return time.Unix(days*3*24*3600, 0).UTC()
	case Timeframe1w:
		// Truncate to Monday of the week
		weekday := int(t.Weekday())
		if weekday == 0 {
			weekday = 7 // Sunday = 7
		}
		return t.AddDate(0, 0, -(weekday-1)).Truncate(24 * time.Hour)
	case Timeframe1mo:
		// Truncate to first day of the month
		return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
	default:
		return t.Truncate(time.Minute)
	}
}
