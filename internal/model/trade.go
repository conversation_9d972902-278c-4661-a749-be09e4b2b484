package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Trade represents a trading transaction (stored in TiDB for OLTP operations)
type Trade struct {
	ID          uuid.UUID       `gorm:"type:char(36);primary_key;default:(UUID())" json:"id"`
	UserID      uuid.UUID       `gorm:"type:char(36);not null;index" json:"user_id"`
	Symbol      string          `gorm:"type:varchar(20);not null;index" json:"symbol"`
	Side        string          `gorm:"type:varchar(10);not null" json:"side"` // buy, sell
	Type        string          `gorm:"type:varchar(20);not null" json:"type"` // market, limit, stop
	Quantity    decimal.Decimal `gorm:"type:decimal(20,8);not null" json:"quantity"`
	Price       decimal.Decimal `gorm:"type:decimal(20,8)" json:"price"`
	FilledQty   decimal.Decimal `gorm:"type:decimal(20,8);default:0" json:"filled_qty"`
	Status      string          `gorm:"type:varchar(20);default:'pending'" json:"status"`
	ExecutedAt  *time.Time      `json:"executed_at"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	DeletedAt   gorm.DeletedAt  `gorm:"index" json:"deleted_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

func (Trade) TableName() string {
	return "trades"
}

// BeforeCreate hook to generate UUID if not provided
func (t *Trade) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return nil
}
