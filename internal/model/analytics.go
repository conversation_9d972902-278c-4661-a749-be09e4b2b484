package model

import (
	"time"
)

// TradeAnalytics represents aggregated trading data (stored in ClickHouse for OLAP operations)
// This struct is used for inserting/querying ClickHouse data, not GORM operations
type TradeAnalytics struct {
	Date        time.Time `json:"date"`
	Symbol      string    `json:"symbol"`
	TotalVolume float64   `json:"total_volume"`
	TotalTrades int64     `json:"total_trades"`
	AvgPrice    float64   `json:"avg_price"`
	HighPrice   float64   `json:"high_price"`
	LowPrice    float64   `json:"low_price"`
	OpenPrice   float64   `json:"open_price"`
	ClosePrice  float64   `json:"close_price"`
	BuyVolume   float64   `json:"buy_volume"`
	SellVolume  float64   `json:"sell_volume"`
	BuyTrades   int64     `json:"buy_trades"`
	SellTrades  int64     `json:"sell_trades"`
	CreatedAt   time.Time `json:"created_at"`
}

// UserAnalytics represents user activity analytics (stored in ClickHouse)
type UserAnalytics struct {
	Date            time.Time `json:"date"`
	UserID          string    `json:"user_id"`
	TotalTrades     int64     `json:"total_trades"`
	TotalVolume     float64   `json:"total_volume"`
	ProfitLoss      float64   `json:"profit_loss"`
	ActiveSymbols   int32     `json:"active_symbols"`
	SessionDuration int64     `json:"session_duration"` // in seconds
	CreatedAt       time.Time `json:"created_at"`
}

// MarketData represents real-time market data (stored in ClickHouse)
type MarketData struct {
	Timestamp time.Time `json:"timestamp"`
	Symbol    string    `json:"symbol"`
	Price     float64   `json:"price"`
	Volume    float64   `json:"volume"`
	Bid       float64   `json:"bid"`
	Ask       float64   `json:"ask"`
	High24h   float64   `json:"high_24h"`
	Low24h    float64   `json:"low_24h"`
	Change24h float64   `json:"change_24h"`
}

// ClickHouse table creation queries
const (
	CreateTradeAnalyticsTable = `
		CREATE TABLE IF NOT EXISTS trade_analytics (
			date Date,
			symbol String,
			total_volume Float64,
			total_trades Int64,
			avg_price Float64,
			high_price Float64,
			low_price Float64,
			open_price Float64,
			close_price Float64,
			buy_volume Float64,
			sell_volume Float64,
			buy_trades Int64,
			sell_trades Int64,
			created_at DateTime
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(date)
		ORDER BY (date, symbol)
	`

	CreateUserAnalyticsTable = `
		CREATE TABLE IF NOT EXISTS user_analytics (
			date Date,
			user_id String,
			total_trades Int64,
			total_volume Float64,
			profit_loss Float64,
			active_symbols Int32,
			session_duration Int64,
			created_at DateTime
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(date)
		ORDER BY (date, user_id)
	`

	CreateMarketDataTable = `
		CREATE TABLE IF NOT EXISTS market_data (
			timestamp DateTime,
			symbol String,
			price Float64,
			volume Float64,
			bid Float64,
			ask Float64,
			high_24h Float64,
			low_24h Float64,
			change_24h Float64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMMDD(timestamp)
		ORDER BY (symbol, timestamp)
	`

	// Kline table creation templates
	CreateKlineTable1m = `
		CREATE TABLE IF NOT EXISTS kline_1m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable3m = `
		CREATE TABLE IF NOT EXISTS kline_3m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable5m = `
		CREATE TABLE IF NOT EXISTS kline_5m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable15m = `
		CREATE TABLE IF NOT EXISTS kline_15m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable30m = `
		CREATE TABLE IF NOT EXISTS kline_30m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1h = `
		CREATE TABLE IF NOT EXISTS kline_1h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable2h = `
		CREATE TABLE IF NOT EXISTS kline_2h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable4h = `
		CREATE TABLE IF NOT EXISTS kline_4h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable8h = `
		CREATE TABLE IF NOT EXISTS kline_8h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable12h = `
		CREATE TABLE IF NOT EXISTS kline_12h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1d = `
		CREATE TABLE IF NOT EXISTS kline_1d (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable3d = `
		CREATE TABLE IF NOT EXISTS kline_3d (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1w = `
		CREATE TABLE IF NOT EXISTS kline_1w (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1mo = `
		CREATE TABLE IF NOT EXISTS kline_1mo (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`
)

// GetAllKlineTableCreationSQL returns all kline table creation SQL statements
func GetAllKlineTableCreationSQL() []string {
	return []string{
		CreateKlineTable1m,
		CreateKlineTable3m,
		CreateKlineTable5m,
		CreateKlineTable15m,
		CreateKlineTable30m,
		CreateKlineTable1h,
		CreateKlineTable2h,
		CreateKlineTable4h,
		CreateKlineTable8h,
		CreateKlineTable12h,
		CreateKlineTable1d,
		CreateKlineTable3d,
		CreateKlineTable1w,
		CreateKlineTable1mo,
	}
}
