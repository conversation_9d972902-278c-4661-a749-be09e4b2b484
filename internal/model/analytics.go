package model

// ClickHouse table creation queries
const (

	// Kline table creation templates
	CreateKlineTable1m = `
		CREATE TABLE IF NOT EXISTS kline_1m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable3m = `
		CREATE TABLE IF NOT EXISTS kline_3m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable5m = `
		CREATE TABLE IF NOT EXISTS kline_5m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable15m = `
		CREATE TABLE IF NOT EXISTS kline_15m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable30m = `
		CREATE TABLE IF NOT EXISTS kline_30m (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1h = `
		CREATE TABLE IF NOT EXISTS kline_1h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable2h = `
		CREATE TABLE IF NOT EXISTS kline_2h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable4h = `
		CREATE TABLE IF NOT EXISTS kline_4h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable8h = `
		CREATE TABLE IF NOT EXISTS kline_8h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable12h = `
		CREATE TABLE IF NOT EXISTS kline_12h (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1d = `
		CREATE TABLE IF NOT EXISTS kline_1d (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable3d = `
		CREATE TABLE IF NOT EXISTS kline_3d (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1w = `
		CREATE TABLE IF NOT EXISTS kline_1w (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`

	CreateKlineTable1mo = `
		CREATE TABLE IF NOT EXISTS kline_1mo (
			timestamp DateTime,
			symbol String,
			open Float64,
			high Float64,
			low Float64,
			close Float64,
			volume Float64,
			trades_count Int64
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(timestamp)
		ORDER BY (symbol, timestamp)
	`
)

// GetAllKlineTableCreationSQL returns all kline table creation SQL statements
func GetAllKlineTableCreationSQL() []string {
	return []string{
		CreateKlineTable1m,
		CreateKlineTable3m,
		CreateKlineTable5m,
		CreateKlineTable15m,
		CreateKlineTable30m,
		CreateKlineTable1h,
		CreateKlineTable2h,
		CreateKlineTable4h,
		CreateKlineTable8h,
		CreateKlineTable12h,
		CreateKlineTable1d,
		CreateKlineTable3d,
		CreateKlineTable1w,
		CreateKlineTable1mo,
	}
}
