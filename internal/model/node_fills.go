package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// NodeFill represents a trade fill record from the node_fills table in TiDB
// This matches the Hyperliquid exchange data structure
type NodeFill struct {
	ID            int64           `gorm:"primaryKey;autoIncrement" json:"id"`
	UserAddress   string          `gorm:"column:user_address;type:varchar(42);index" json:"user_address"`
	Hash          string          `gorm:"column:hash;type:varchar(66);index" json:"hash"`
	TID           int64           `gorm:"column:tid;index" json:"tid"`                             // Trade ID from exchange
	Coin          string          `gorm:"column:coin;type:varchar(20);not null;index" json:"coin"` // Symbol
	Px            decimal.Decimal `gorm:"column:px;type:decimal(20,8);not null" json:"px"`         // Price
	Sz            decimal.Decimal `gorm:"column:sz;type:decimal(20,8);not null" json:"sz"`         // Size/Quantity
	Side          string          `gorm:"column:side;type:char(1);not null" json:"side"`           // "B" for buy, "A" for ask/sell
	Time          int64           `gorm:"column:time;not null;index" json:"time"`                  // Unix timestamp in milliseconds
	StartPosition decimal.Decimal `gorm:"column:start_position;type:decimal(20,8)" json:"start_position"`
	Direction     string          `gorm:"column:direction;type:varchar(20)" json:"direction"`
	ClosedPnl     decimal.Decimal `gorm:"column:closed_pnl;type:decimal(20,8)" json:"closed_pnl"`
	OID           int64           `gorm:"column:oid" json:"oid"` // Order ID
	Crossed       int             `gorm:"column:crossed" json:"crossed"`
	Fee           decimal.Decimal `gorm:"column:fee;type:decimal(20,8);default:0" json:"fee"`
	FeeToken      string          `gorm:"column:fee_token;type:varchar(10)" json:"fee_token"`
	TradeDate     string          `gorm:"column:trade_date;type:date;index" json:"trade_date"`
	TradeType     string          `gorm:"column:trade_type;type:varchar(20);index" json:"trade_type"` // "perpetual" or "spot"
	ProcessedAt   time.Time       `gorm:"column:processed_at" json:"processed_at"`
}

func (NodeFill) TableName() string {
	return "node_fills"
}

// GetExecutedTime converts the Unix millisecond timestamp to time.Time
func (nf *NodeFill) GetExecutedTime() time.Time {
	return time.Unix(nf.Time/1000, (nf.Time%1000)*1000000)
}

// GetQuoteVolume calculates the quote volume (price * quantity)
func (nf *NodeFill) GetQuoteVolume() decimal.Decimal {
	return nf.Px.Mul(nf.Sz)
}

// IsValidForKline checks if the fill has all required data for kline generation
func (nf *NodeFill) IsValidForKline() bool {
	return nf.Coin != "" &&
		!nf.Px.IsZero() &&
		!nf.Sz.IsZero() &&
		nf.Time > 0
}

// IsBuy returns true if this is a buy order
func (nf *NodeFill) IsBuy() bool {
	return nf.Side == "B"
}

// IsSell returns true if this is a sell order
func (nf *NodeFill) IsSell() bool {
	return nf.Side == "A"
}
