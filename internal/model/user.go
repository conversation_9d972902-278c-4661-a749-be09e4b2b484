package model

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the system (stored in TiDB for OLTP operations)
type User struct {
	ID        uuid.UUID      `gorm:"type:char(36);primary_key;default:(UUID())" json:"id"`
	Email     *string        `gorm:"type:varchar(255);uniqueIndex" json:"email"`
	Name      string         `gorm:"type:varchar(255);not null" json:"name"`
	Status    string         `gorm:"type:varchar(50);default:'active'" json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (User) TableName() string {
	return "users"
}

// BeforeCreate hook to generate UUID if not provided
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}
