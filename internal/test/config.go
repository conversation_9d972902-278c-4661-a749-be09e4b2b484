package test

import (
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/xbit-dex/xbit-hypertrader-go/config"
	"github.com/xbit-dex/xbit-hypertrader-go/internal/global"
)

type TestConfig struct {
	Server *config.Server
}

func SetupTestConfig() *TestConfig {

	testConfig := &TestConfig{
		Server: &config.Server{
			JWT: config.JWT{
				SigningKey:  "test-secret-key",
				ExpiresTime: "24h",
				BufferTime:  "1h",
				Issuer:      "xbit-hypertrader-test",
			},
			System: config.System{
				Env:                "test",
				Addr:               8080,
				RouterPrefix:       "/api/xbit-hypertrader",
				GraphqlPrefix:      "/api/xbit-hypertrader/graphql",
				AdminGraphqlPrefix: "/api/xbit-hypertrader/admin/graphql",
			},
			TiDB: config.TiDB{
				Host:         "localhost",
				Port:         "3306",
				Username:     "test",
				Password:     "test",
				Dbname:       "test_db",
				MaxIdleConns: 5,
				MaxOpenConns: 10,
				MaxLifetime:  "1h",
			},
			ClickHouse: config.ClickHouse{
				Host:         "localhost",
				Port:         "8123",
				Database:     "test_db",
				Username:     "default",
				Password:     "",
				MaxIdleConns: 2,
				MaxOpenConns: 5,
				MaxLifetime:  "1h",
				Secure:       false,
				SkipVerify:   true,
			},
		},
	}

	global.GVA_CONFIG = *testConfig.Server
	return testConfig
}

func SetupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic("Failed to connect to test database: " + err.Error())
	}

	global.GVA_DB_TIDB = db
	return db
}

func CleanupTestDB() {
	if global.GVA_DB_TIDB != nil {
		sqlDB, err := global.GVA_DB_TIDB.DB()
		if err == nil {
			sqlDB.Close()
		}
		global.GVA_DB_TIDB = nil
	}

	if global.GVA_DB_CLICKHOUSE != nil {
		global.GVA_DB_CLICKHOUSE.Close()
		global.GVA_DB_CLICKHOUSE = nil
	}
}
