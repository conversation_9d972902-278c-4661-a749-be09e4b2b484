package test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type TestHelper struct {
	t *testing.T
}

func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

func (h *TestHelper) AssertNoError(err error) {
	assert.NoError(h.t, err)
}

func (h *TestHelper) AssertError(err error) {
	assert.Error(h.t, err)
}

func (h *TestHelper) AssertEqual(expected, actual interface{}) {
	assert.Equal(h.t, expected, actual)
}

func (h *TestHelper) AssertNotNil(object interface{}) {
	assert.NotNil(h.t, object)
}

func (h *TestHelper) AssertNil(object interface{}) {
	assert.Nil(h.t, object)
}

func (h *TestHelper) RequireNoError(err error) {
	require.NoError(h.t, err)
}

func (h *TestHelper) CreateTestContext() context.Context {
	return context.Background()
}

func (h *TestHelper) CreateTestContextWithUserID(userID uuid.UUID) context.Context {
	ctx := context.Background()
	return context.WithValue(ctx, "userId", userID.String())
}

func (h *TestHelper) GenerateTestUUID() uuid.UUID {
	return uuid.New()
}

// Setup functions
func SetupTest(t *testing.T) (*TestConfig, *TestFixtures, *TestHelper) {
	config := SetupTestConfig()
	fixtures := NewTestFixtures()
	helper := NewTestHelper(t)
	return config, fixtures, helper
}

func SetupTestWithDB(t *testing.T) (*TestConfig, *TestFixtures, *TestHelper) {
	config := SetupTestConfig()
	SetupTestDB()
	fixtures := NewTestFixtures()
	helper := NewTestHelper(t)
	return config, fixtures, helper
}

func TeardownTest() {
	CleanupTestDB()
}
