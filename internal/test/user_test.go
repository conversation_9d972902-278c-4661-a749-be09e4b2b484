package test

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

func TestUser_BeforeCreate(t *testing.T) {
	_, fixtures, helper := SetupTestWithDB(t)
	defer TeardownTest()

	user := fixtures.CreateTestUser()
	user.ID = uuid.Nil // Reset ID to test auto-generation

	// Simulate GORM's BeforeCreate hook
	err := user.BeforeCreate(nil)
	
	helper.AssertNoError(err)
	helper.AssertNotNil(user.ID)
	assert.NotEqual(t, uuid.Nil, user.ID)
}

func TestUser_TableName(t *testing.T) {
	user := &model.User{}
	assert.Equal(t, "users", user.TableName())
}

func TestCreateTestUser(t *testing.T) {
	_, fixtures, helper := SetupTest(t)

	user := fixtures.CreateTestUser()

	helper.AssertNotNil(user)
	helper.AssertNotNil(user.Email)
	helper.AssertEqual("Test User", user.Name)
	helper.AssertEqual("active", user.Status)
	assert.NotEqual(t, uuid.Nil, user.ID)
}
