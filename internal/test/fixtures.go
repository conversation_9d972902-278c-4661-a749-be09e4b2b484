package test

import (
	"time"

	"github.com/google/uuid"

	"github.com/xbit-dex/xbit-hypertrader-go/internal/model"
)

type TestFixtures struct{}

func NewTestFixtures() *TestFixtures {
	return &TestFixtures{}
}

func (f *TestFixtures) CreateTestUser() *model.User {
	userID := uuid.New()
	email := "<EMAIL>"

	return &model.User{
		ID:        userID,
		Email:     &email,
		Name:      "Test User",
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

func (f *TestFixtures) CreateTestUserWithEmail(email string) *model.User {
	user := f.CreateTestUser()
	user.Email = &email
	return user
}

func (f *TestFixtures) CreateTestTradeAnalytics() *model.TradeAnalytics {
	return &model.TradeAnalytics{
		Date:        time.Now().Truncate(24 * time.Hour),
		Symbol:      "BTC/USDT",
		TotalVolume: 1000000.0,
		TotalTrades: 150,
		AvgPrice:    50000.0,
		HighPrice:   52000.0,
		LowPrice:    48000.0,
		OpenPrice:   49000.0,
		ClosePrice:  51000.0,
		BuyVolume:   600000.0,
		SellVolume:  400000.0,
		BuyTrades:   90,
		SellTrades:  60,
		CreatedAt:   time.Now(),
	}
}

func (f *TestFixtures) CreateTestUserAnalytics(userID string) *model.UserAnalytics {
	return &model.UserAnalytics{
		Date:            time.Now().Truncate(24 * time.Hour),
		UserID:          userID,
		TotalTrades:     25,
		TotalVolume:     50000.0,
		ProfitLoss:      1500.0,
		ActiveSymbols:   5,
		SessionDuration: 3600, // 1 hour
		CreatedAt:       time.Now(),
	}
}

func (f *TestFixtures) CreateTestMarketData() *model.MarketData {
	return &model.MarketData{
		Timestamp: time.Now(),
		Symbol:    "BTC/USDT",
		Price:     50000.0,
		Volume:    1.5,
		Bid:       49950.0,
		Ask:       50050.0,
		High24h:   52000.0,
		Low24h:    48000.0,
		Change24h: 2.5,
	}
}
