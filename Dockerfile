FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git make

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the applications
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/xbit-hypertrader cmd/graphql/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/xbit-hypertrader-scheduler cmd/scheduler/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main/xbit-hypertrader-worker cmd/worker_kline/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates
RUN apk --no-cache add ca-certificates curl

WORKDIR /root/

# Copy binaries and configuration
COPY --from=builder /app/main/ .
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/entrypoint.sh .
COPY env/ env/

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Expose port
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["./entrypoint.sh"]

# Default command
CMD ["./xbit-hypertrader"]
