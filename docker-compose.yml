version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - STAGE=docker
    depends_on:
      - nats
    volumes:
      - ./env:/root/env
    networks:
      - xbit-network

  scheduler:
    build: .
    command: ["./xbit-hypertrader-scheduler"]
    environment:
      - STAGE=docker
    depends_on:
      - nats
    volumes:
      - ./env:/root/env
    networks:
      - xbit-network

  worker:
    build: .
    command: ["./xbit-hypertrader-worker"]
    environment:
      - STAGE=docker
    depends_on:
      - nats
    volumes:
      - ./env:/root/env
    networks:
      - xbit-network

  kline-worker:
    build: .
    command: ["./xbit-hypertrader-worker"]
    environment:
      - STAGE=docker
    depends_on:
      - nats
    volumes:
      - ./env:/root/env
    networks:
      - xbit-network

  nats:
    image: nats:2.9-alpine
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - nats_data:/data
    networks:
      - xbit-network

volumes:
  nats_data:

networks:
  xbit-network:
    driver: bridge
