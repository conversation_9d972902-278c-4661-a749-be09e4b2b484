# xbit-hypertrader-go

A high-performance trading platform built with Go, featuring dual database architecture for optimal OLTP and OLAP workloads.

## Architecture Overview

This project implements a modern microservices architecture with:

- **TiDB**: MySQL-compatible distributed database for transactional workloads (OLTP)
- **ClickHouse**: Columnar database for analytics and real-time data processing (OLAP)
- **GraphQL API**: Modern API layer with type-safe queries
- **NATS JetStream**: Message queue for asynchronous processing
- **Background Jobs**: Scheduled tasks for data processing and maintenance

## Database Configuration

### TiDB (OLTP)
- **Host**: **********:4000
- **Purpose**: User management, trade execution, order management
- **Features**: ACID compliance, horizontal scaling, MySQL compatibility

### ClickHouse (OLAP)
- **Host**: ***********:8123
- **Purpose**: Analytics, market data, real-time aggregations
- **Features**: Columnar storage, high-performance analytics, real-time queries

## Quick Start

### Prerequisites
- Go 1.21+
- Access to TiDB and C<PERSON>House instances
- NATS server (optional, for message queue features)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/xbit-dex/xbit-hypertrader-go.git
cd xbit-hypertrader-go
```

2. Install dependencies:
```bash
make deps
```

3. Create database tables:
```bash
make db-create-tables
```

4. Run the application:
```bash
make run-local
```

### Development

Start the development server with hot reload:
```bash
make dev
```

Run tests:
```bash
make test
```

Build all services:
```bash
make build-all
```

Run individual services:
```bash
make run-scheduler    # Run scheduler
make run-kline       # Run kline worker
```

## Services

### GraphQL Server
- **Port**: 8080
- **Endpoint**: `/api/xbit-hypertrader/graphql`
- **Health Check**: `/health`

### Kline Worker
- **Purpose**: Processes trade fills from TiDB and generates OHLCV candlestick data
- **Data Source**: `node_fills` table (Hyperliquid exchange data)
- **Output**: ClickHouse kline tables for 14 different timeframes
- **Timeframes**: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 8h, 12h, 1d, 3d, 1w, 1mo
- **Processing**: Real-time via NATS messages + periodic batch processing

### Scheduler
Background job scheduler for:
- Market data synchronization
- Analytics aggregation
- Data cleanup tasks

### Worker
Message queue worker for:
- Trade processing
- Market data ingestion
- Real-time analytics

## Environment Configuration

The application uses environment-based configuration. Copy and modify the environment files:

- `env/local.env` - Local development
- `env/docker.env` - Docker deployment
- `env/staging.env` - Staging environment
- `env/prod.env` - Production environment

## Docker Deployment

Build and run with Docker Compose:
```bash
make docker-build
make docker-run
```

## Project Structure

```
├── cmd/                    # Application entry points
│   ├── graphql/           # GraphQL server
│   ├── scheduler/         # Background job scheduler
│   ├── worker_example/    # Message queue worker
│   └── cli/              # CLI tools
├── config/               # Configuration structures
├── internal/             # Private application code
│   ├── initializer/      # Database and service initializers
│   ├── model/           # Database models
│   ├── nats/            # Message queue client
│   ├── task/            # Background task scheduler
│   └── test/            # Test utilities
├── env/                 # Environment configuration files
├── scripts/             # Build and deployment scripts
└── migrations/          # Database migrations
```

## Testing

Run the test suite:
```bash
make test
```

Run tests with coverage:
```bash
make test-coverage
```

## Contributing

1. Follow the existing code structure and patterns
2. Write tests for new functionality
3. Update documentation as needed
4. Use the provided Makefile targets for common tasks

## License

[Add your license information here]