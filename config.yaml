# JWT Configuration
jwt:
  signing-key: {{ index . "JWT_SECRET" | default "your-secret-key" }}
  expires-time: {{ index . "JWT_EXPIRES_TIME" | default "7d" }}
  buffer-time: {{ index . "JWT_BUFFER_TIME" | default "1d" }}
  issuer: {{ index . "JWT_ISSUER" | default "xbit-hypertrader" }}

# System Configuration
system:
  env: {{ index . "APP_ENV" | default "local" }}
  addr: {{ index . "SERVER_PORT" | default "8080" }}
  router-prefix: "/api/xbit-hypertrader"
  graphql-prefix: "/api/xbit-hypertrader/graphql"
  admin-graphql-prefix: "/api/xbit-hypertrader/admin/graphql"

# TiDB Configuration (OLTP - Transactional workloads)
tidb:
  host: {{ index . "TIDB_HOST" | default "127.0.0.1" }}
  port: {{ index . "TIDB_PORT" | default "4000" }}
  config: {{ index . "TIDB_CONFIG" | default "charset=utf8mb4&parseTime=True&loc=Local" }}
  db-name: {{ index . "TIDB_DB" | default "xbit_hypertrader" }}
  username: {{ index . "TIDB_USER" | default "root" }}
  password: {{ index . "TIDB_PASS" | default "" }}
  max-idle-conns: {{ index . "TIDB_MAX_IDLE_CONNS" | default "10" }}
  max-open-conns: {{ index . "TIDB_MAX_OPEN_CONNS" | default "100" }}
  max-lifetime: {{ index . "TIDB_MAX_LIFETIME" | default "1h" }}

# ClickHouse Configuration (OLAP - Analytics workloads)
clickhouse:
  host: {{ index . "CLICKHOUSE_HOST" | default "127.0.0.1" }}
  port: {{ index . "CLICKHOUSE_PORT" | default "8123" }}
  database: {{ index . "CLICKHOUSE_DB" | default "default" }}
  username: {{ index . "CLICKHOUSE_USER" | default "default" }}
  password: {{ index . "CLICKHOUSE_PASS" | default "" }}
  max-idle-conns: {{ index . "CLICKHOUSE_MAX_IDLE_CONNS" | default "5" }}
  max-open-conns: {{ index . "CLICKHOUSE_MAX_OPEN_CONNS" | default "50" }}
  max-lifetime: {{ index . "CLICKHOUSE_MAX_LIFETIME" | default "1h" }}
  secure: {{ index . "CLICKHOUSE_SECURE" | default "false" }}
  skip-verify: {{ index . "CLICKHOUSE_SKIP_VERIFY" | default "true" }}

# NATS Configuration
nats:
  url: {{ index . "NATS_URL" | default "nats://127.0.0.1:4222" }}
  user: {{ index . "NATS_USER" | default "" }}
  pass: {{ index . "NATS_PASS" | default "" }}
  use-tls: {{ index . "NATS_USE_TLS" | default "false" }}
  token: {{ index . "NATS_TOKEN" | default "" }}

# Scheduled Tasks
cron-tasks:
  - id: "cleanup_old_data"
    cron: "0 0 2 * * *"    # Daily at 2 AM
