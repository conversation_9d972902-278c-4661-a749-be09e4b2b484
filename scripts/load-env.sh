#!/bin/bash
set -e

ENV=${1:-local}
ENV_FILE="env/${ENV}.env"

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: Environment file '$ENV_FILE' not found!"
    exit 1
fi

echo "Loading environment variables from: $ENV_FILE"
export $(grep -v '^#' "$ENV_FILE" | grep -v '^$' | xargs)

# Verify critical variables
REQUIRED_VARS=(
    "APP_ENV"
    "SERVER_PORT"
    "TIDB_HOST"
    "TIDB_PORT"
    "TIDB_USER"
    "TIDB_PASS"
    "TIDB_DB"
    "CLICKHOUSE_HOST"
    "CLICKHOUSE_PORT"
    "CLICKHOUSE_USER"
    "CLICKHOUSE_PASS"
    "CLICKHOUSE_DB"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable '$var' is not set!"
        exit 1
    fi
done

echo "Environment variables loaded successfully!"
export STAGE="$ENV"
